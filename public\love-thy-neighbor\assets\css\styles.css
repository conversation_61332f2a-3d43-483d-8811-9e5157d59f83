/**
 * <PERSON>bor WNC - Main Stylesheet
 * Faith-based advocacy for immigrant rights and justice in Western North Carolina
 */

/* CSS Variables */
:root {
    --primary: #2a6a3c;
    --secondary: #5aa76d;
    --accent: #e63946;
    --light: #f1faee;
    --dark: #1d3557;
    --neutral: #a8dadc;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: #f9f9f9;
    color: #333;
    line-height: 1.6;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Header Styles */
header {
    background-color: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.logo {
    display: flex;
    align-items: center;
}

.logo-image {
    width: 45px;
    height: 45px;
    margin-right: 12px;
    border-radius: 4px;
    object-fit: contain;
    background: rgba(42, 106, 60, 0.1);
    padding: 3px;
    transition: all 0.3s ease;
}

.logo-image:hover {
    transform: scale(1.05);
    background: rgba(42, 106, 60, 0.2);
}

.org-name {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary);
}

/* Navigation Styles */
.main-nav {
    display: flex;
    list-style: none;
}

.nav-item {
    margin-left: 1.5rem;
    position: relative;
}

.nav-item a {
    text-decoration: none;
    color: var(--dark);
    font-weight: 500;
    transition: color 0.3s;
    padding: 0.5rem 0;
    display: block;
}

.nav-item a:hover {
    color: var(--secondary);
}

/* Dropdown Menu Styles */
.has-submenu > a::after {
    content: ' ▼';
    font-size: 0.8rem;
    margin-left: 0.5rem;
}

.submenu {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    border-radius: 4px;
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
    list-style: none;
    padding: 0.5rem 0;
}

.nav-item:hover .submenu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.submenu li {
    margin: 0;
}

.submenu a {
    padding: 0.5rem 1rem;
    color: var(--dark);
    font-weight: 400;
    border-bottom: none;
}

.submenu a:hover {
    background-color: var(--light);
    color: var(--primary);
}

/* Ensure desktop hover functionality works properly */
@media (min-width: 769px) {
    .nav-item:hover .submenu {
        opacity: 1 !important;
        visibility: visible !important;
        transform: translateY(0) !important;
        max-height: none !important;
    }
}

/* Hero Section */
.hero {
    background:
        linear-gradient(135deg, rgba(42, 106, 60, 0.8), rgba(90, 167, 109, 0.6)),
        linear-gradient(45deg, rgba(29, 53, 87, 0.3), rgba(168, 218, 220, 0.4)),
        url('../banner-background.png');
    background-size: contain, contain, contain;
    background-position: center, center, center;
    background-blend-mode: overlay, multiply, normal;
    color: white;
    padding: 5rem 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(241, 250, 238, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(90, 167, 109, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(42, 106, 60, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.hero h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    position: relative;
    z-index: 1;
}

.hero p {
    font-size: 1.2rem;
    max-width: 800px;
    margin: 0 auto 2rem;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
    position: relative;
    z-index: 1;
    background: rgba(255,255,255,0.1);
    padding: 1rem 2rem;
    border-radius: 10px;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255,255,255,0.2);
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
    position: relative;
    z-index: 1;
}

.btn {
    padding: 0.8rem 1.5rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s;
    border: none;
    cursor: pointer;
    display: inline-block;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--accent), #c1121f);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #c1121f, var(--accent));
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(230, 57, 70, 0.4);
}

.btn-secondary {
    background: rgba(255,255,255,0.1);
    border: 2px solid rgba(255,255,255,0.6);
    color: white;
}

.btn-secondary:hover {
    background: rgba(255,255,255,0.9);
    color: var(--dark);
    border-color: white;
    transform: translateY(-2px);
}

/* Section Styles */
section {
    padding: 4rem 0;
}

.section-title {
    text-align: center;
    margin-bottom: 3rem;
    color: var(--primary);
}

.mission {
    background-color: white;
    text-align: center;
}

.mission-statement {
    max-width: 800px;
    margin: 0 auto;
    font-size: 1.1rem;
    line-height: 1.8;
}

/* Yard Signs Section */
.yard-signs {
    background:
        linear-gradient(45deg, rgba(241, 250, 238, 0.9), rgba(168, 218, 220, 0.8)),
        url('https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    position: relative;
}

.yard-signs::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 70% 30%, rgba(42, 106, 60, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 30% 70%, rgba(90, 167, 109, 0.08) 0%, transparent 50%);
    pointer-events: none;
}

.campaign-container {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    justify-content: center;
}

.campaign-card {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: transform 0.3s;
    width: 300px;
    text-align: center;
}

.campaign-card:hover {
    transform: translateY(-5px);
}

.card-img {
    height: 180px;
    background-color: var(--neutral);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--dark);
    font-weight: bold;
    overflow: hidden;
}

.card-img img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 4px;
}

.card-content {
    padding: 1.5rem;
}

.card-content h3 {
    color: var(--primary);
    margin-bottom: 0.8rem;
}

.card-content p {
    margin-bottom: 1.2rem;
}

.price {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--primary);
    margin: 1rem 0;
}

/* Social Media Template Styling */
.social-template {
    background: var(--neutral) !important;
    padding: 2rem !important;
    border-radius: 8px !important;
    margin-top: 2rem !important;
}

.social-template h3 {
    color: var(--primary) !important;
    margin-bottom: 1rem !important;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.social-template .template-text {
    background: white !important;
    padding: 1.5rem !important;
    border-radius: 4px !important;
    border-left: 4px solid var(--primary) !important;
    font-family: 'Georgia', serif !important;
    line-height: 1.6 !important;
    font-size: 1rem;
    color: var(--dark) !important;
}

.social-template .template-note {
    margin-top: 1rem !important;
    font-size: 0.9rem !important;
    color: var(--dark-gray) !important;
    font-style: italic;
}

/* Hero Stats Styling */
.hero-stats {
    margin-top: 2rem !important;
    display: flex !important;
    gap: 2rem !important;
    justify-content: center !important;
    flex-wrap: wrap !important;
}

.hero-stats > div {
    text-align: center !important;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    min-width: 120px;
}

.hero-stats .stat-number {
    font-size: 2rem !important;
    font-weight: bold !important;
    color: var(--accent) !important;
    display: block;
}

.hero-stats .stat-label {
    font-size: 0.9rem !important;
    margin-top: 0.25rem;
    opacity: 0.9;
}

/* Hero Section Bible Verse Slider Integration */
.hero .bible-verses-slider {
    margin-top: 1.5rem;
    margin-bottom: 1.5rem;
}

.hero .splide__slide blockquote {
    background: rgba(0, 0, 0, 0.5) !important;
    border: 1px solid rgba(255, 255, 255, 0.25) !important;
    backdrop-filter: blur(10px) !important;
    max-width: 500px;
    margin: 0 auto;
}

/* Hero Text Background Treatment */
.hero h1 {
    background: rgba(0, 0, 0, 0.4);
    padding: 1rem 1.5rem;
    border-radius: 8px;
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: inline-block;
    margin-bottom: 1rem;
}

.hero p {
    background: rgba(0, 0, 0, 0.4);
    padding: 0.75rem 1.25rem;
    border-radius: 6px;
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: inline-block;
    margin-bottom: 1rem;
}

/* Partners Section */
.partners {
    background-color: white;
}

.partners-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.partner-card {
    text-align: center;
    padding: 2rem;
    border: 1px solid #eee;
    border-radius: 8px;
}

.partner-logo {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    background-color: #ddd;
    margin: 0 auto 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #777;
}

.social-links {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 1rem;
}

.social-icon {
    width: 40px;
    height: 40px;
    background-color: var(--primary);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
}

/* Mutual Aid Section */
.mutual-aid {
    background-color: var(--light);
}

.aid-opportunities {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.aid-card {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    padding: 1.5rem;
}

.aid-card h3 {
    color: var(--primary);
    margin-bottom: 1rem;
}

/* Footer */
footer {
    background-color: var(--dark);
    color: white;
    padding: 3rem 0 1.5rem;
}

.footer-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-column h3 {
    margin-bottom: 1.2rem;
    font-size: 1.2rem;
}

.footer-column ul {
    list-style: none;
}

.footer-column li {
    margin-bottom: 0.8rem;
}

.footer-column a {
    color: #ddd;
    text-decoration: none;
}

.footer-column a:hover {
    color: white;
    text-decoration: underline;
}

.copyright {
    text-align: center;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(255,255,255,0.1);
    font-size: 0.9rem;
    color: #aaa;
}

iframe {
    width: 100%;
    max-width: 560px;
    text-align: center;
    /* height: auto; */
}

/* Language Switcher */
.language-switcher {
    position: relative;
    display: inline-block;
    margin-left: 1rem;
}

.language-dropdown {
    background-color: var(--primary);
    color: white;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
}

.language-dropdown:hover {
    background-color: var(--secondary);
}

/* Mobile Menu Toggle Button */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    justify-content: space-around;
    width: 30px;
    height: 30px;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 0;
    z-index: 101;
    transition: all 0.3s ease;
}

.hamburger-line {
    width: 100%;
    height: 3px;
    background-color: var(--primary);
    border-radius: 2px;
    transition: all 0.3s ease;
    transform-origin: center;
}

/* Hamburger animation when menu is open */
.mobile-menu-toggle.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-toggle.active .hamburger-line:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.6);
    backdrop-filter: blur(3px);
    overflow-y: auto;
    padding: 20px 0;
}

.modal-content {
    background-color: white;
    margin: 2rem auto;
    padding: 2rem;
    border-radius: 12px;
    width: 90%;
    max-width: 600px;
    position: relative;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    max-height: calc(100vh - 4rem);
    overflow-y: auto;
}

.close {
    color: #999;
    float: right;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    position: absolute;
    top: 15px;
    right: 20px;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close:hover,
.close:focus {
    color: var(--accent);
    background-color: rgba(230, 57, 70, 0.1);
}

.modal h2 {
    color: var(--primary);
    margin-bottom: 0.5rem;
    font-size: 1.8rem;
    font-weight: 600;
}

.modal p {
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.5;
}

/* Form Layout */
.form-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    margin-bottom: 1rem;
    flex: 1;
}

.form-group-half {
    flex: 1;
}

.form-group-third {
    flex: 0 0 30%;
}

.form-group-two-thirds {
    flex: 0 0 65%;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--dark);
    font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    font-size: 1rem;
    transition: all 0.3s ease;
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(42, 106, 60, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 60px;
}

.order-submit-btn {
    width: 100%;
    margin-top: 1rem;
    padding: 1rem;
    font-size: 1.1rem;
    font-weight: 600;
}

/* Fourth Amendment Resources */
.fourth-amendment {
    background:
        linear-gradient(135deg, rgba(255,255,255,0.95), rgba(241, 250, 238, 0.9)),
        url('https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    position: relative;
}

.fourth-amendment::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 80% 20%, rgba(29, 53, 87, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 20% 80%, rgba(42, 106, 60, 0.08) 0%, transparent 50%);
    pointer-events: none;
}

.resources-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.resource-card {
    background: var(--light);
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
    transition: transform 0.3s;
    border: 2px solid transparent;
}

.resource-card:hover {
    transform: translateY(-3px);
    border-color: var(--primary);
}

.resource-icon {
    width: 60px;
    height: 60px;
    background-color: var(--primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: white;
    font-size: 1.5rem;
}

.resource-card h3 {
    color: var(--primary);
    margin-bottom: 1rem;
}

.resource-card p {
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

/* Email Alert Button */
.email-alert-btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: var(--accent);
    color: white;
    border: none;
    border-radius: 50px;
    padding: 1rem 1.5rem;
    font-weight: 600;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    transition: all 0.3s;
    z-index: 999;
}

.email-alert-btn:hover {
    background-color: #c1121f;
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-container {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        position: relative;
    }

    /* Show mobile menu toggle button */
    .mobile-menu-toggle {
        display: flex;
        order: 3;
    }

    /* Hide navigation by default on mobile */
    .main-navigation {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        border-radius: 0 0 8px 8px;
        opacity: 0;
        visibility: hidden;
        transform: translateY(-10px);
        transition: all 0.3s ease;
        z-index: 100;
        max-height: 0;
        overflow: hidden;
    }

    /* Show navigation when mobile menu is open */
    .main-navigation.mobile-nav-open {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
        max-height: calc(100vh - 80px); /* Account for header height */
        overflow-y: auto;
        -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
    }

    .main-nav {
        margin-top: 1rem;
        flex-direction: column;
        align-items: stretch;
        padding: 1rem;
    }

    .nav-item {
        margin: 0.25rem 0;
        width: 100%;
    }

    .nav-item a {
        padding: 0.75rem 1rem;
        border-radius: 4px;
        transition: background-color 0.3s ease;
    }

    .nav-item a:hover {
        background-color: var(--light);
    }

    /* Mobile dropdown adjustments */
    .submenu {
        position: static;
        opacity: 0;
        visibility: hidden;
        transform: none;
        box-shadow: none;
        background: var(--light);
        margin-top: 0.5rem;
        border-radius: 4px;
        display: block;
        max-height: 0;
        overflow: hidden;
        transition: all 0.3s ease-in-out;
        border: 1px solid rgba(42, 106, 60, 0.1);
    }

    .nav-item.mobile-open .submenu {
        opacity: 1;
        visibility: visible;
        /* max-height is now set dynamically via JavaScript for smoother animations */
    }

    /* Disable hover on mobile to prevent seizure-inducing loops */
    .main-navigation.mobile-nav-open .nav-item:hover .submenu {
        display: block;
        opacity: 0;
        visibility: hidden;
        max-height: 0;
    }

    .has-submenu > a {
        position: relative;
        cursor: pointer;
        min-height: 44px; /* Minimum touch target size for accessibility */
        display: flex;
        align-items: center;
        padding: 0.75rem 1rem;
        margin: 0.25rem 0;
        border-radius: 4px;
        background: rgba(42, 106, 60, 0.05);
        transition: all 0.3s ease;
        touch-action: manipulation; /* Prevent double-tap zoom */
    }

    .has-submenu > a:hover,
    .has-submenu > a:focus {
        background: rgba(42, 106, 60, 0.1);
    }

    .has-submenu > a::after {
        content: ' ▼';
        transition: transform 0.3s ease;
        margin-left: auto;
        font-size: 0.8rem;
    }

    .nav-item.mobile-open .has-submenu > a::after {
        transform: rotate(180deg);
    }

    /* Improve submenu item touch targets */
    .submenu a {
        min-height: 40px;
        display: flex;
        align-items: center;
        touch-action: manipulation;
    }

    .language-switcher {
        margin: 1rem;
        padding-top: 1rem;
        border-top: 1px solid rgba(42, 106, 60, 0.1);
        text-align: center;
    }

    .language-dropdown {
        width: 100%;
        max-width: 200px;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        text-align: center;
        margin-bottom: 0.5rem;
    }

    .modal-content {
        margin: 1rem auto;
        width: 95%;
        padding: 1.5rem;
        max-height: calc(100vh - 2rem);
    }

    .form-row {
        flex-direction: column;
        gap: 0;
    }

    .form-group-half,
    .form-group-third,
    .form-group-two-thirds {
        flex: 1;
    }

    .modal h2 {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }

    .modal p {
        font-size: 0.9rem;
        margin-bottom: 1.5rem;
    }

    .email-alert-btn {
        bottom: 10px;
        right: 10px;
        padding: 0.8rem 1.2rem;
        font-size: 0.9rem;
    }
}
