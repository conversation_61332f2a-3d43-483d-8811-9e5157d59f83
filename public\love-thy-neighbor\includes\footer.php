<?php
/**
 * Footer include file
 * Contains the site footer with links and contact information
 */

// Include configuration if not already included
if (!defined('SITE_NAME')) {
    require_once 'config.php';
}
?>

<!-- Footer -->
<footer id="contact">
    <div class="container">
        <div class="footer-grid">
            <div class="footer-column">
                <h3><?php echo SITE_NAME; ?></h3>
                <p>Faith-based advocacy for immigrant rights and justice in Western North Carolina.</p>
            </div>
            
            <div class="footer-column">
                <h3>Contact Us</h3>
                <p>Email: <a href="mailto:<?php echo CONTACT_EMAIL; ?>"><?php echo CONTACT_EMAIL; ?></a></p>
                <p>Phone: <a href="tel:<?php echo str_replace(['(', ')', ' ', '-'], '', CONTACT_PHONE); ?>"><?php echo CONTACT_PHONE; ?></a></p>
            </div>
            
            <div class="footer-column">
                <h3>Quick Links</h3>
                <ul>
                    <?php foreach ($footer_links['quick_links'] as $link): ?>
                        <li><a href="<?php echo htmlspecialchars(get_nav_url($link['href'])); ?>"><?php echo htmlspecialchars($link['text']); ?></a></li>
                    <?php endforeach; ?>
                </ul>
            </div>

            <div class="footer-column">
                <h3>Rights Resources</h3>
                <ul>
                    <?php foreach ($footer_links['rights_resources'] as $link): ?>
                        <li>
                            <a href="<?php echo htmlspecialchars(get_nav_url($link['href'])); ?>"
                               <?php echo isset($link['onclick']) ? 'onclick="' . htmlspecialchars($link['onclick']) . '"' : ''; ?>>
                                <?php echo htmlspecialchars($link['text']); ?>
                            </a>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>
        </div>
        
        <div class="copyright">
            <p>&copy; <?php echo get_current_year(); ?> <?php echo SITE_NAME; ?>. All rights reserved.</p>
        </div>
    </div>
</footer>
