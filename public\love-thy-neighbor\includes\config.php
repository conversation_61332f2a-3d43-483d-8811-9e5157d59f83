<?php
/**
 * Configuration file for Love Thy Neighbor WNC website
 */

// Site configuration
define('SITE_NAME', 'Love Thy Neighbor WNC');
define('SITE_TAGLINE', 'Standing with our immigrant neighbors through faith, advocacy, and community support');
define('SITE_URL', 'https://lovethyneighborwnc.org');

// Contact information
define('CONTACT_EMAIL', '<EMAIL>');
define('CONTACT_PHONE', '(*************');

// Social media links
define('FACEBOOK_URL', '#');
define('TWITTER_URL', '#');
define('INSTAGRAM_URL', '#');

// CSS and JS versions for cache busting
define('CSS_VERSION', '1.0.0');
define('JS_VERSION', '1.0.0');

// Default page settings
$default_meta = [
    'title' => SITE_NAME,
    'description' => 'Faith-based advocacy for immigrant rights and justice in Western North Carolina.',
    'keywords' => 'immigrant rights, western north carolina, faith-based advocacy, community support',
    'author' => SITE_NAME
];

// Helper function to get navigation URL with proper path
function get_nav_url($url) {
    // If it's an absolute URL (starts with http) or anchor (#), return as-is
    if (strpos($url, 'http') === 0 || strpos($url, '#') === 0) {
        return $url;
    }

    // If it starts with /, it's already absolute from site root
    if (strpos($url, '/') === 0) {
        return get_base_path() . ltrim($url, '/');
    }

    // Otherwise, it's relative, so add base path
    return get_base_path() . $url;
}

// Navigation menu items
$nav_items = [
    [
        'href' => 'about.php',
        'text' => 'About'
    ],
    [
        'href' => 'take-action.php',
        'text' => 'Take Action',
        'submenu' => [
            ['href' => 'yard-signs.php', 'text' => 'Yard Signs'],
            ['href' => '4a-workplaces.php', 'text' => '4A Campaign'],
            ['href' => 'mutual-aid.php', 'text' => 'Mutual Aid']
        ]
    ],
    [
        'href' => '4a-workplaces/index.php',
        'text' => '4A Workplaces',
        'submenu' => [
            ['href' => '4a-workplaces/index.php', 'text' => 'All Workplaces'],
            ['href' => '4a-workplaces/schools.php', 'text' => 'Schools'],
            ['href' => '4a-workplaces/churches.php', 'text' => 'Churches'],
            ['href' => '4a-workplaces/traditional-businesses.php', 'text' => 'Traditional Businesses'],
            ['href' => '4a-workplaces/homeless-providers.php', 'text' => 'Homeless Providers'],
            ['href' => '4a-workplaces/property-managers.php', 'text' => 'Property Managers']
        ]
    ],
    [
        'href' => 'workplace-rights.php',
        'text' => 'Know Your Rights',
        'submenu' => [
            ['href' => 'workplace-rights.php', 'text' => 'Fourth Amendment Workplaces'],
            ['href' => 'if-agent-shows-up.php', 'text' => 'If An Agent Shows Up'],
            ['href' => 'raids.php', 'text' => 'ICE Raids Guide']
        ]
    ],
    [
        'href' => 'resources.php',
        'text' => 'Resources',
        'submenu' => [
            ['href' => 'resources.php', 'text' => 'All Resources'],
            ['href' => 'if-you-see-something.php', 'text' => 'If You See Something']
        ]
    ],
    [
        'href' => 'contact.php',
        'text' => 'Contact'
    ]
];

// Footer links
$footer_links = [
    'quick_links' => [
        ['href' => 'about.php', 'text' => 'About'],
        ['href' => 'yard-signs.php', 'text' => 'Yard Signs'],
        ['href' => '4a-workplaces.php', 'text' => '4A Campaign'],
        ['href' => '4a-workplaces/index.php', 'text' => '4A Workplaces'],
        ['href' => 'workplace-rights.php', 'text' => 'Know Your Rights'],
        ['href' => 'resources.php', 'text' => 'Resources'],
        ['href' => 'mutual-aid.php', 'text' => 'Mutual Aid'],
        ['href' => 'contact.php', 'text' => 'Contact']
    ],
    'rights_resources' => [
        ['href' => '4a-workplaces.php', 'text' => '4A Campaign'],
        ['href' => '4a-workplaces/index.php', 'text' => '4A Workplaces'],
        ['href' => '4a-workplaces/schools.php', 'text' => 'Schools'],
        ['href' => '4a-workplaces/churches.php', 'text' => 'Churches'],
        ['href' => '4a-workplaces/traditional-businesses.php', 'text' => 'Traditional Businesses'],
        ['href' => '4a-workplaces/homeless-providers.php', 'text' => 'Homeless Providers'],
        ['href' => '4a-workplaces/property-managers.php', 'text' => 'Property Managers'],
        ['href' => 'workplace-rights.php', 'text' => 'Workplace Rights'],
        ['href' => 'if-agent-shows-up.php', 'text' => 'If An Agent Shows Up'],
        ['href' => 'raids.php', 'text' => 'ICE Raids Guide'],
        ['href' => 'if-you-see-something.php', 'text' => 'If You See Something'],
        ['href' => '#', 'text' => 'Email Alerts', 'onclick' => 'openEmailModal()']
    ]
];

// Language options
$languages = [
    'en' => 'English',
    'es' => 'Español'
];

// Helper function to get page title
function get_page_title($page_title = '', $separator = ' - ') {
    global $default_meta;
    if (empty($page_title)) {
        return $default_meta['title'];
    }
    return $page_title . $separator . $default_meta['title'];
}

// Helper function to get current year
function get_current_year() {
    return date('Y');
}

// Helper function to get the correct base path for assets
function get_base_path() {
    // Get the current file path
    $current_file = $_SERVER['PHP_SELF'];

    // Simple check: if we're in a subdirectory, we need to go up one level
    if (strpos($current_file, '/about/') !== false ||
        strpos($current_file, '/take-action/') !== false ||
        strpos($current_file, '/partners/') !== false ||
        strpos($current_file, '/resources/') !== false ||
        strpos($current_file, '/mutual-aid/') !== false ||
        strpos($current_file, '/contact/') !== false ||
        strpos($current_file, '/4a-workplaces/') !== false) {
        return '../';
    }

    // If we're in the root directory, no prefix needed
    return '';
}

// Helper function to get asset URL with proper path
function get_asset_url($asset_path) {
    return get_base_path() . $asset_path;
}

// Helper function to get internal page URL with proper path
function get_page_url($page_path) {
    return get_nav_url($page_path);
}
?>
