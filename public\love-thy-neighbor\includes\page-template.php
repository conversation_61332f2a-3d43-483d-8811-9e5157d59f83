<?php
/**
 * Page Template for Love Thy Neighbor WNC
 * Copy this file and customize for new pages
 */

// Page-specific variables - CUSTOMIZE THESE FOR EACH PAGE
$page_title = 'Page Title';  // Will be appended to site name
$page_description = 'Description of this specific page for SEO and social media.';
$page_keywords = 'page specific, keywords, here';
$body_class = 'page-specific-class';  // Optional: add page-specific CSS class

// Optional: Additional CSS files specific to this page
// $additional_css = ['assets/css/page-specific.css'];

// Optional: Additional JS files specific to this page
// $additional_js = ['assets/js/page-specific.js'];

// Optional: Inline JavaScript for this page
// $inline_js = 'console.log("Page-specific JavaScript");';

// Include the head section
include 'includes/head.php';
?>

<?php include 'includes/header.php'; ?>

<!-- Page Content Starts Here -->

<!-- Hero Section (Optional) -->
<section class="hero">
    <div class="container">
        <h1>Page Title</h1>
        <p>Page subtitle or description</p>
    </div>
</section>

<!-- Main Content Section -->
<section class="main-content">
    <div class="container">
        <h2 class="section-title">Section Title</h2>
        
        <!-- Add your page content here -->
        <div class="content">
            <p>Your page content goes here...</p>
        </div>
    </div>
</section>

<!-- Additional Sections as needed -->
<section class="additional-section">
    <div class="container">
        <h2 class="section-title">Another Section</h2>
        
        <div class="content">
            <p>More content...</p>
        </div>
    </div>
</section>

<!-- Page Content Ends Here -->

<?php include 'includes/footer.php'; ?>

<?php include 'includes/scripts.php'; ?>
