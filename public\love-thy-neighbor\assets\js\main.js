/**
 * Love Thy Neighbor WNC - Main JavaScript
 * Faith-based advocacy for immigrant rights and justice in Western North Carolina
 */

// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeLanguageSwitcher();
    initializeModalEvents();
    initializeMobileNavigation();
    initializeMobileMenuToggle();

    // Language initialization is now handled in initializeLanguageSwitcher()
    // to respect saved preferences and improve browser compatibility
});

// Language Switcher
function initializeLanguageSwitcher() {
    const languageSelect = document.getElementById('languageSelect');
    if (languageSelect) {
        // Add multiple event listeners for better browser compatibility
        const handleLanguageChange = function() {
            const selectedLang = this.value;
            console.log('Language changed to:', selectedLang);

            // Store language preference in localStorage for persistence
            try {
                localStorage.setItem('preferredLanguage', selectedLang);
            } catch (e) {
                console.log('localStorage not available:', e);
            }

            if (selectedLang === 'es') {
                console.log('Translating to Spanish...');
                translateToSpanish();
            } else {
                console.log('Translating to English...');
                translateToEnglish();
            }
        };

        // Use both 'change' and 'input' events for better compatibility
        languageSelect.addEventListener('change', handleLanguageChange);
        languageSelect.addEventListener('input', handleLanguageChange);

        // For older browsers, also listen to 'click' on options
        languageSelect.addEventListener('click', function() {
            // Small delay to ensure value has changed
            setTimeout(handleLanguageChange.bind(this), 10);
        });

        // Load saved language preference on page load
        try {
            const savedLang = localStorage.getItem('preferredLanguage');
            if (savedLang && (savedLang === 'en' || savedLang === 'es')) {
                languageSelect.value = savedLang;
                // Trigger translation based on saved preference
                if (savedLang === 'es') {
                    translateToSpanish();
                } else {
                    translateToEnglish();
                }
            } else {
                // Default to English if no preference is saved
                languageSelect.value = 'en';
                translateToEnglish();
            }
        } catch (e) {
            console.log('localStorage not available:', e);
            // Fallback to English if localStorage fails
            languageSelect.value = 'en';
            translateToEnglish();
        }
    }
}

// Mobile Navigation
function initializeMobileNavigation() {
    // Only apply mobile navigation behavior on smaller screens
    function isMobile() {
        return window.innerWidth <= 768;
    }

    // Handle submenu toggles on mobile
    const hasSubmenuItems = document.querySelectorAll('.has-submenu > a');

    hasSubmenuItems.forEach(function(menuItem) {
        // Prevent double-tap zoom on iOS
        menuItem.style.touchAction = 'manipulation';

        // Handle both click and touch events for better mobile compatibility
        const handleMenuToggle = function(e) {
            if (isMobile()) {
                e.preventDefault();
                e.stopPropagation();

                const parentItem = this.closest('.nav-item');
                const isOpen = parentItem.classList.contains('mobile-open');

                // Close all other open menus with animation
                document.querySelectorAll('.nav-item.mobile-open').forEach(function(item) {
                    if (item !== parentItem) {
                        item.classList.remove('mobile-open');
                        // Add a small delay to allow for smooth transitions
                        setTimeout(() => {
                            const submenu = item.querySelector('.submenu');
                            if (submenu) {
                                submenu.style.maxHeight = '0';
                            }
                        }, 10);
                    }
                });

                // Toggle current menu with smooth animation
                if (isOpen) {
                    parentItem.classList.remove('mobile-open');
                    const submenu = parentItem.querySelector('.submenu');
                    if (submenu) {
                        submenu.style.maxHeight = '0';
                    }
                } else {
                    parentItem.classList.add('mobile-open');
                    const submenu = parentItem.querySelector('.submenu');
                    if (submenu) {
                        // Calculate the actual height needed
                        submenu.style.maxHeight = 'none';
                        const height = submenu.scrollHeight;
                        submenu.style.maxHeight = '0';
                        // Force reflow
                        submenu.offsetHeight;
                        submenu.style.maxHeight = height + 'px';
                    }
                }
            }
        };

        // Add event listeners for both click and touch
        menuItem.addEventListener('click', handleMenuToggle);
        menuItem.addEventListener('touchend', function(e) {
            // Prevent the click event from firing after touchend
            e.preventDefault();
            handleMenuToggle.call(this, e);
        });
    });

    // Close mobile menus when clicking outside
    const closeAllMobileMenus = function() {
        document.querySelectorAll('.nav-item.mobile-open').forEach(function(item) {
            item.classList.remove('mobile-open');
            const submenu = item.querySelector('.submenu');
            if (submenu) {
                submenu.style.maxHeight = '0';
            }
        });
    };

    document.addEventListener('click', function(e) {
        if (isMobile() && !e.target.closest('.main-nav')) {
            closeAllMobileMenus();
        }
    });

    document.addEventListener('touchstart', function(e) {
        if (isMobile() && !e.target.closest('.main-nav')) {
            closeAllMobileMenus();
        }
    });

    // Reset mobile menu state on window resize
    window.addEventListener('resize', function() {
        if (!isMobile()) {
            document.querySelectorAll('.nav-item.mobile-open').forEach(function(item) {
                item.classList.remove('mobile-open');
                const submenu = item.querySelector('.submenu');
                if (submenu) {
                    submenu.style.maxHeight = '';
                }
            });
        }
    });
}

// Mobile Menu Toggle
function initializeMobileMenuToggle() {
    const mobileMenuToggle = document.getElementById('mobileMenuToggle');
    const mainNavigation = document.getElementById('mainNavigation');

    if (mobileMenuToggle && mainNavigation) {
        // Handle mobile menu toggle
        const toggleMobileMenu = function(e) {
            e.preventDefault();
            e.stopPropagation();

            const isOpen = mainNavigation.classList.contains('mobile-nav-open');

            if (isOpen) {
                // Close menu
                mainNavigation.classList.remove('mobile-nav-open');
                mobileMenuToggle.classList.remove('active');
                mobileMenuToggle.setAttribute('aria-expanded', 'false');

                // Close all open submenus
                document.querySelectorAll('.nav-item.mobile-open').forEach(function(item) {
                    item.classList.remove('mobile-open');
                    const submenu = item.querySelector('.submenu');
                    if (submenu) {
                        submenu.style.maxHeight = '0';
                    }
                });
            } else {
                // Open menu
                mainNavigation.classList.add('mobile-nav-open');
                mobileMenuToggle.classList.add('active');
                mobileMenuToggle.setAttribute('aria-expanded', 'true');
            }
        };

        // Add event listeners for both click and touch
        mobileMenuToggle.addEventListener('click', toggleMobileMenu);
        mobileMenuToggle.addEventListener('touchend', function(e) {
            e.preventDefault();
            toggleMobileMenu.call(this, e);
        });

        // Set initial aria attributes
        mobileMenuToggle.setAttribute('aria-expanded', 'false');

        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (window.innerWidth <= 768 &&
                !e.target.closest('.main-navigation') &&
                !e.target.closest('.mobile-menu-toggle')) {
                mainNavigation.classList.remove('mobile-nav-open');
                mobileMenuToggle.classList.remove('active');
                mobileMenuToggle.setAttribute('aria-expanded', 'false');

                // Close all open submenus
                document.querySelectorAll('.nav-item.mobile-open').forEach(function(item) {
                    item.classList.remove('mobile-open');
                    const submenu = item.querySelector('.submenu');
                    if (submenu) {
                        submenu.style.maxHeight = '0';
                    }
                });
            }
        });

        // Close mobile menu on window resize if switching to desktop
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                mainNavigation.classList.remove('mobile-nav-open');
                mobileMenuToggle.classList.remove('active');
                mobileMenuToggle.setAttribute('aria-expanded', 'false');
            }
        });
    }
}

// Modal Functions
function initializeModalEvents() {
    // Close modal when clicking outside
    window.onclick = function(event) {
        const emailModal = document.getElementById('emailModal');
        const yardSignModal = document.getElementById('yardSignModal');

        if (emailModal && event.target == emailModal) {
            emailModal.style.display = 'none';
        }
        if (yardSignModal && event.target == yardSignModal) {
            yardSignModal.style.display = 'none';
        }
    }
}

function openEmailModal() {
    const modal = document.getElementById('emailModal');
    if (modal) {
        modal.style.display = 'block';
    }
}

function closeEmailModal() {
    const modal = document.getElementById('emailModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// Email Form Submission
function submitEmailForm(event) {
    event.preventDefault();
    const formData = new FormData(event.target);
    const data = Object.fromEntries(formData);

    // Here you would typically send the data to your server
    console.log('Email signup data:', data);

    // For now, just show a success message
    alert('Thank you for signing up! You will receive email alerts about important community updates.');
    closeEmailModal();
    event.target.reset();

    // TODO: Implement actual form submission to server
    // Example: submitToServer(data);
}

// Yard Sign Modal Functions
function openYardSignModal() {
    const modal = document.getElementById('yardSignModal');
    if (modal) {
        modal.style.display = 'block';
    }
}

function closeYardSignModal() {
    const modal = document.getElementById('yardSignModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// Yard Sign Order Form Submission
function submitYardSignOrder(event) {
    event.preventDefault();
    const formData = new FormData(event.target);
    const data = Object.fromEntries(formData);

    // Calculate total cost for display
    const prices = {
        'standard': 15,
        'window': 5,
        'banner': 45
    };
    const signType = data.signType;
    const quantity = parseInt(data.quantity);
    const unitPrice = prices[signType] || 0;
    const totalCost = unitPrice * quantity;

    // Submit to server
    fetch('process-order.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            alert(`Thank you for your order! Order #${result.orderNumber}\n\nTotal: $${result.totalCost}\nFulfillment: ${result.fulfillmentMethod}\n\nWe'll send you an invoice and contact you soon. Please check your email for confirmation.`);
            closeYardSignModal();
            event.target.reset();
        } else {
            alert('There was an error processing your order: ' + result.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('There was an error submitting your order. Please try again or contact us directly.');
    });
}

// Fourth Amendment Resource Functions
function openWorkplaceInfo() {
    const message = `Workplace Rights Information:

• Employers generally cannot search personal belongings without consent
• You have the right to refuse unreasonable searches
• Know your company policies regarding privacy
• Contact legal aid if you believe your rights were violated`;
    
    alert(message);
}

function openSchoolInfo() {
    const message = `School Rights Information:

• Students have limited Fourth Amendment protections in schools
• Schools can search lockers and bags with reasonable suspicion
• Parents have rights regarding their children's privacy
• Know your school district's search policies`;
    
    alert(message);
}

function openChurchInfo() {
    const message = `Church Rights Information:

• Religious institutions have strong constitutional protections
• Sanctuary policies provide some protection for immigrants
• Know your congregation's policies and procedures
• Religious freedom includes protection from unreasonable searches`;
    
    alert(message);
}

function openRaidInfo() {
    const message = `If You See A Raid:

• Stay calm and observe from a safe distance
• Do not interfere with law enforcement
• Document what you see (video/photos if safe)
• Contact legal aid organizations immediately
• Know emergency contact numbers
• Provide support to affected families`;
    
    alert(message);
}

// Spanish Translation System
function translateToSpanish() {
    // General site elements
    const orgName = document.querySelector('.org-name');
    const heroTitle = document.querySelector('.hero h1');
    const heroSubtitle = document.querySelector('.hero p');

    if (orgName) orgName.textContent = 'Ama a Tu Prójimo WNC';
    if (heroTitle && !heroTitle.hasAttribute('data-translate')) {
        heroTitle.textContent = 'Ama a Tu Prójimo Carolina del Norte Occidental';
    }
    if (heroSubtitle && !heroSubtitle.hasAttribute('data-translate')) {
        heroSubtitle.textContent = 'Apoyando a nuestros vecinos inmigrantes a través de la fe, la defensa y el apoyo comunitario';
    }

    // Fourth Amendment page specific translations
    const translations = {
        'hero-title': 'Conozca Sus Derechos de la Cuarta Enmienda',
        'hero-subtitle': 'Comprenda sus protecciones constitucionales en diferentes entornos y situaciones',
        'btn-workplace': 'Derechos en el Trabajo',
        'btn-emergency': 'Información de Emergencia',
        'btn-alerts': 'Recibir Alertas',

        'overview-title': 'Sus Derechos Constitucionales',
        'overview-p1': 'La Cuarta Enmienda de la Constitución de los Estados Unidos protege a todas las personas en los Estados Unidos de registros e incautaciones irrazonables por parte de funcionarios gubernamentales. Estos derechos se aplican independientemente del estatus migratorio.',
        'overview-p2': 'Comprender sus derechos y cómo se aplican en diferentes entornos puede ayudar a protegerlo a usted, su familia y su comunidad. El conocimiento es poder, y conocer sus derechos es el primer paso para protegerlos.',
        'overview-p3': '<strong>Esta información también está disponible en español.</strong> Toda la información sobre derechos y procedimientos en el lugar de trabajo está disponible tanto en inglés como en español para garantizar que todos en nuestra comunidad puedan acceder a esta información vital.',

        'workplace-title': 'Derechos en el Lugar de Trabajo',

        // Workplace Rights Cards
        'rights-card1-title': 'Sus Derechos Fundamentales',
        'rights-card1-desc': 'No está obligado a cumplir con solicitudes ilegales de agentes federales. Estos derechos se aplican a todos los trabajadores independientemente del estatus migratorio.',
        'rights-card1-li1': '<strong>Permanecer en silencio:</strong> No se está obligado a responder preguntas sobre el estado migratorio, el país de origen o la identidad de las personas presentes',
        'rights-card1-li2': '<strong>Retención de documentos:</strong> No se debe presentar identificación, tarjetas de tiempo, horarios u otros registros a menos que sea requerido por la ley',
        'rights-card1-li3': '<strong>Grabar interacción:</strong> Es posible grabar y almacenar imágenes de cualquier encuentro',
        'rights-card1-li4': '<strong>Alertar a otros:</strong> El personal tiene el derecho de informar a otros sobre la presencia de agentes federales',

        'rights-card2-title': 'Requisitos de Órdenes Judiciales',
        'rights-card2-desc': 'Los agentes no tienen permiso para ingresar a áreas privadas sin una orden judicial válida. Comprender los tipos de órdenes es crucial para proteger su lugar de trabajo.',
        'rights-card2-li1': '<strong>Orden judicial:</strong> Firmada por un juez, debe indicar "Tribunal de Distrito de EE. UU." en la parte superior',
        'rights-card2-li2': '<strong>Orden administrativa (I-200):</strong> Firmada por un agente del DHS/ICE - NO es necesario cumplir',
        'rights-card2-li3': '<strong>Restringir el acceso:</strong> El personal puede decir que no está autorizado para permitir la entrada a áreas privadas',
        'rights-card2-li4': '<strong>Solicitar asesoramiento legal:</strong> Puede pedir que cualquier orden judicial sea evaluada por un asesor legal antes de conceder acceso',

        'rights-card3-title': 'Advertencia de Riesgo Legal',
        'rights-card3-desc': 'Aunque tiene muchos derechos, ciertas acciones podrían exponerle a riesgos legales. Sepa qué evitar.',
        'rights-card3-li1': '<strong>Rechazar la entrada</strong> después de que se presente una orden judicial válida',
        'rights-card3-li2': '<strong>Proporcionar información incorrecta</strong> a agentes federales',
        'rights-card3-li3': '<strong>Asistir a alguien en su escape</strong> de las fuerzas del orden',
        'rights-card3-li4': '<strong>Recuerde:</strong> Puede rechazar registros y permanecer en silencio sin consecuencias legales',

        // Procedures Section
        'procedures-title': 'Si Entra un Agente Federal a Su Lugar de Trabajo',

        'proc-card1-title': 'Acciones Inmediatas',
        'proc-card1-li1': '<strong>Grabe la interacción</strong> - Documente todo de manera segura',
        'proc-card1-li2': '<strong>Solicite identificación:</strong> "¿Con qué agencia está? ¿Puede mostrarme una identificación?"',
        'proc-card1-li3': '<strong>Solicite orden judicial:</strong> "Muéstreme la orden judicial, por favor"',
        'proc-card1-li4': '<strong>Cierre con llave las puertas interiores</strong> que separan las áreas públicas de las privadas',

        'proc-card2-title': 'Revisión de Orden Judicial',
        'proc-card2-li1': '<strong>Orden judicial:</strong> Debe estar firmada por un juez con "Tribunal de Distrito de EE. UU." en la parte superior',
        'proc-card2-li2': '<strong>Orden administrativa (I-200):</strong> NO es necesario cumplir',
        'proc-card2-li3': '<strong>Si hay orden judicial válida:</strong> Consulte con asesor legal antes de conceder acceso',
        'proc-card2-li4': '<strong>Monitoree el cumplimiento:</strong> Asegúrese de que los agentes solo registren áreas listadas en la orden',

        'proc-card3-title': 'Proteger a Todos',
        'proc-card3-li1': '<strong>Alerte a todo el personal:</strong> "A todo el personal, mantenga la calma y siga nuestros protocolos con respecto a agentes federales"',
        'proc-card3-li2': '<strong>Informe en voz alta:</strong> "No es necesario responder preguntas ni presentar identificación"',
        'proc-card3-li3': '<strong>NO colabore con los agentes</strong> en la clasificación de las personas según su estatus migratorio o país de origen',
        'proc-card3-li4': '<strong>Indique claramente:</strong> "No tiene mi permiso para entrar" a áreas privadas',

        // Workplace Readiness Checklist
        'readiness-title': 'Verificación de la Preparación para Lugares de Trabajo',
        'readiness-desc': 'Prepare su lugar de trabajo para proteger los derechos de todos. Use esta lista de verificación para asegurar que su organización esté lista.',

        'ready-card1-title': 'Preparaciones Físicas',
        'ready-card1-li1': '✓ Instalar carteles que señalen áreas PRIVADAS',
        'ready-card1-li2': '✓ Instalar cerraduras en puertas interiores que separen áreas públicas y privadas',
        'ready-card1-li3': '✓ Asegurar que el personal pueda resguardar rápidamente las áreas privadas',
        'ready-card1-li4': '✓ Publicar protocolos claros que todo el personal debe cumplir',

        'ready-card2-title': 'Capacitación del Personal',
        'ready-card2-li1': '✓ Designar persona cualificada para gestionar las interacciones con los agentes',
        'ready-card2-li2': '✓ Capacitar al personal sobre protocolos con agentes federales',
        'ready-card2-li3': '✓ Establecer sistema de notificación rápida para el personal',
        'ready-card2-li4': '✓ Practicar procedimientos de emergencia regularmente',

        'ready-card3-title': 'Preparación Legal',
        'ready-card3-li1': '✓ Tener información de contacto de asesor legal fácilmente disponible',
        'ready-card3-li2': '✓ Conocer la diferencia entre tipos de órdenes judiciales',
        'ready-card3-li3': '✓ Comprender sus derechos y limitaciones',
        'ready-card3-li4': '✓ Mantener números de contacto de emergencia publicados',

        // If An Agent Shows Up page translations
        'agent-hero-title': 'Si Aparece un Agente',
        'agent-hero-subtitle': 'Guía de respuesta de emergencia para encuentros con agentes federales',
        'btn-immediate': 'Acciones Inmediatas',
        'btn-contacts': 'Contactos de Emergencia',

        'agent-overview-title': 'Manténgase Seguro y Conozca Sus Derechos',
        'agent-overview-p1': 'Si aparecen agentes federales en su lugar de trabajo, hogar o en su comunidad, es importante mantener la calma y conocer sus derechos. Esta guía proporciona instrucciones paso a paso para protegerse a usted y a otros.',
        'agent-overview-p2': 'Recuerde: Usted tiene derechos constitucionales independientemente de su estatus migratorio. Estos derechos lo protegen de registros e incautaciones irrazonables por parte de funcionarios gubernamentales.',
        'agent-overview-p3': '<strong>Esta información también está disponible en español.</strong> Toda la información de respuesta de emergencia está disponible tanto en inglés como en español para garantizar que todos en nuestra comunidad puedan acceder a esta información vital.',

        'immediate-title': 'Acciones Inmediatas',
        'action-card1-title': 'Documente Todo',
        'action-card1-desc': 'Si es seguro hacerlo, grabe la interacción y documente lo que ve.',
        'action-card1-li1': '<strong>Filme la interacción</strong> - Documente todo de manera segura desde la distancia',
        'action-card1-li2': '<strong>Anote detalles:</strong> Hora, ubicación, número de agentes, números de placa',
        'action-card1-li3': '<strong>Guarde el video:</strong> Suba inmediatamente al almacenamiento en la nube',
        'action-card1-li4': '<strong>Comparta con asistencia legal:</strong> Contacte organizaciones de apoyo',

        'action-card2-title': 'Verifique la Autoridad',
        'action-card2-desc': 'Siempre pida identificación y verifique cualquier orden judicial presentada.',
        'action-card2-li1': '<strong>Pida identificación:</strong> "¿De qué agencia es? ¿Puede mostrarme una identificación?"',
        'action-card2-li2': '<strong>Solicite orden judicial:</strong> "Por favor muéstreme la orden judicial"',
        'action-card2-li3': '<strong>Verifique validez:</strong> La orden judicial debe estar firmada por un juez',
        'action-card2-li4': '<strong>Orden administrativa (I-200):</strong> NO tiene que cumplir',

        'action-card3-title': 'Alerte y Proteja',
        'action-card3-desc': 'Advierta a otros y ayude a proteger los derechos de todos.',
        'action-card3-li1': '<strong>Alerte a otros en voz alta:</strong> "Hay agentes federales aquí"',
        'action-card3-li2': '<strong>Informe de derechos:</strong> "No tiene que responder preguntas o mostrar identificación"',
        'action-card3-li3': '<strong>Cierre áreas privadas:</strong> Asegure puertas interiores si es posible',
        'action-card3-li4': '<strong>Pida ayuda:</strong> Contacte asistencia legal y familiares',

        'rights-title': 'Sus Derechos Durante un Encuentro',
        'rights-card1-title': 'Derecho a Permanecer en Silencio',
        'rights-card1-desc': 'No está obligado a responder preguntas sobre estatus migratorio, país de origen, o quién está presente.',
        'rights-card1-li1': 'Diga: "Estoy ejerciendo mi derecho a permanecer en silencio"',
        'rights-card1-li2': 'No proporcione información sobre otros',
        'rights-card1-li3': 'Puede pedir un intérprete',
        'rights-card1-li4': 'El silencio no puede ser usado en su contra',

        'rights-card2-title': 'Derecho a Rechazar Registros',
        'rights-card2-desc': 'No tiene que consentir a registros de su persona, pertenencias, o áreas privadas sin una orden judicial válida.',
        'rights-card2-li1': 'Diga: "No consiento a un registro"',
        'rights-card2-li2': 'No muestre identificación a menos que sea legalmente requerido',
        'rights-card2-li3': 'No abra puertas o contenedores',
        'rights-card2-li4': 'Pida ver una orden judicial válida',

        'rights-card3-title': 'Derecho a Asesoría Legal',
        'rights-card3-desc': 'Tiene derecho a hablar con un abogado y a que un asesor legal revise cualquier orden judicial.',
        'rights-card3-li1': 'Diga: "Quiero hablar con mi abogado"',
        'rights-card3-li2': 'Pida revisión de orden judicial por asesor legal',
        'rights-card3-li3': 'Contacte organizaciones de asistencia legal',
        'rights-card3-li4': 'No firme nada sin revisión legal',

        'contacts-title': 'Contactos de Emergencia',
        'contacts-desc': 'Mantenga estos números fácilmente disponibles y compártalos con familiares y compañeros de trabajo.',
        'contact-card1-title': 'Línea de Crisis',
        'contact-card1-desc': 'Apoyo de emergencia 24/7 para situaciones de aplicación de inmigración',
        'contact-card2-title': 'Asistencia Legal',
        'contact-card2-desc': 'Asistencia legal gratuita para problemas de inmigración y derechos constitucionales',
        'contact-card3-title': 'Apoyo Comunitario',
        'contact-card3-desc': 'Organizaciones locales que brindan asistencia inmediata y recursos',

        // Workplace Rights page translations
        'workplace-hero-title': 'Derechos de la Cuarta Enmienda en el Lugar de Trabajo',
        'workplace-hero-subtitle': 'Protegiendo los derechos constitucionales en el lugar de trabajo',
        'btn-rights': 'Sus Derechos',
        'btn-readiness': 'Preparación del Lugar de Trabajo',

        'workplace-overview-title': 'Sus Protecciones Constitucionales en el Trabajo',
        'workplace-overview-p1': 'La Cuarta Enmienda de la Constitución de los Estados Unidos protege a todas las personas en los Estados Unidos de registros e incautaciones irrazonables por parte de funcionarios gubernamentales. Estos derechos se aplican en entornos laborales independientemente del estatus migratorio.',
        'workplace-overview-p2': 'Comprender sus derechos y cómo se aplican en su lugar de trabajo puede ayudar a protegerlo a usted, sus compañeros de trabajo y su comunidad. El conocimiento es poder, y conocer sus derechos es el primer paso para protegerlos.',
        'workplace-overview-p3': '<strong>Esta información también está disponible en español.</strong> Toda la información y procedimientos sobre derechos en el lugar de trabajo están disponibles tanto en inglés como en español para garantizar que todos en nuestra comunidad puedan acceder a esta información vital.',

        // Resources page translations
        'resources-hero-title': 'Recursos y Herramientas',
        'resources-hero-subtitle': 'Materiales educativos y guías para apoyar el trabajo de justicia para inmigrantes',
        'immigrant-resources-title': 'Recursos para Inmigrantes',
        'know-rights-cards-title': 'Tarjetas de Conozca Sus Derechos',
        'know-rights-cards-desc': 'Tarjetas imprimibles con información esencial de derechos en inglés y español. Mantenga en billetera o cartera.',
        'btn-download-pdf': 'Descargar PDF',
        'facts-matter-title': 'Los Hechos Importan',
        'facts-matter-desc': 'Lea los 7 Hechos sobre Inmigración que Todo Estadounidense Debe Saber - Haga Clic Aquí',
        'btn-read-facts': 'Leer Hechos',
        'whose-faith-title': '¿La Fe de Quién Importa? La Lucha por la Libertad Religiosa Más Allá de la Derecha Cristiana',
        'whose-faith-quote': '"La investigación basada en datos afirma el papel esencial que los inmigrantes, independientemente de su estatus legal, desempeñan en el fortalecimiento de la economía estadounidense, enriqueciendo las comunidades y contribuyendo a la fuerza laboral de la nación. Los inmigrantes pagan miles de millones en impuestos, apoyan programas vitales como el Seguro Social y ayudan a impulsar la innovación, el trabajo de cuidado y el desarrollo de pequeñas empresas. Las políticas que apuntan a los inmigrantes para su remoción a menudo resultan en daños generalizados: separando familias, interrumpiendo las economías locales y socavando la estabilidad comunitaria. Al mismo tiempo, una clara mayoría de estadounidenses apoya políticas de inmigración inclusivas, incluidos los caminos hacia la ciudadanía, reconociendo que nuestro futuro compartido es más seguro cuando se basa en la dignidad, la oportunidad y la equidad. Continúe leyendo para conocer siete hechos clave sobre la inmigración que todo estadounidense debe saber."',
        'whose-faith-attribution': '— Clara Alvarez Caraveo, estudiante de doctorado en Sociología de USC y Jody Agius Vallejo, Profesora de Sociología',
        'whose-faith-description': '_Este informe está destinado como una_ **_herramienta práctica_** _para abogados de inmigración, abogados de defensa criminal, defensores de derechos civiles, organizaciones basadas en la fe y casas de culto._',
        'btn-read-facts-2': 'Leer Hechos',
        'collective-statement-title': 'Declaración Colectiva de Comunidades de Fe en Apoyo a la Justicia para Inmigrantes',
        'collective-statement-desc': 'Unidos en la fe y arraigados en el amor, nos comprometemos a continuar con la práctica centenaria de las comunidades cristianas caminando junto a refugiados e inmigrantes en su búsqueda de seguridad y dignidad. Nos comprometemos a restaurar y promover la hospitalidad y la bienvenida a quienes buscan refugio, independientemente de dónde vengan, cómo oren o qué idioma hablen.',
        'btn-view-guide': 'Ver Guía',
        'educational-rights-title': 'Derechos Educativos',
        'educational-rights-p1': '<strong>Como organizaciones e instituciones basadas en la fe arraigadas en Carolina del Norte Occidental y que representan una diversidad de tradiciones y creencias religiosas, estamos unidos en nuestro compromiso de trabajar hacia un mundo humano y justo para todos, independientemente del estatus migratorio.</strong>',
        'educational-rights-p2': 'Creemos que todas las personas son Hijos Amados de Dios, miembros importantes y vitales de nuestra comunidad, y merecedores del pleno disfrute de los derechos humanos y la dignidad humana. Creemos en amar al prójimo, dar la bienvenida al extranjero, proporcionar refugio seguro para aquellos que huyen de la opresión, cuidar a los vulnerables entre nosotros y luchar contra la injusticia.',
        'btn-read-statement': 'Leer Declaración Completa',
        'faith-toolkit-title': 'Herramientas para Comunidades de Fe',
        'faith-toolkit-desc': 'Recursos para ayudar a las comunidades de fe a ser más acogedoras y solidarias con los vecinos inmigrantes, incluyendo políticas de santuario, programas educativos y herramientas de defensa.',
        'sanctuary-policies-title': 'Políticas de Santuario',
        'sanctuary-policies-desc': 'Plantillas de políticas y procedimientos para congregaciones que consideran la designación de santuario.',
        'btn-download-toolkit': 'Descargar Herramientas',
        'educational-curricula-title': 'Currículos Educativos',
        'educational-curricula-desc': 'Estudios bíblicos, guías de discusión y materiales educativos sobre inmigración y fe.',
        'btn-access-materials': 'Acceder a Materiales',
        'accompaniment-training-title': 'Entrenamiento de Acompañamiento',
        'accompaniment-training-desc': 'Materiales de entrenamiento para miembros de congregaciones que quieren acompañar a inmigrantes a la corte o a citas con ICE.',
        'btn-get-training': 'Obtener Entrenamiento',
        '4a-workplaces-title': 'Recursos de la Campaña de Lugares de Trabajo 4A',
        '4a-workplaces-desc': 'Recursos integrales para empresas y trabajadores para entender y proteger los derechos de la Cuarta Enmienda en entornos laborales. Únase a nuestra creciente red de lugares de trabajo santuario constitucionales.',
        '4a-campaign-title': 'Campaña de Lugares de Trabajo 4A',
        '4a-campaign-desc': 'Aprenda sobre nuestra iniciativa principal para crear lugares de trabajo santuario constitucionales. Más de 50 empresas ya se han unido a nuestra red.',
        'btn-join-campaign': 'Únase a la Campaña',
        'employer-toolkit-title': 'Herramientas para Empleadores',
        'employer-toolkit-desc': 'Kit completo para empleadores que incluye políticas, procedimientos, materiales de entrenamiento e información de certificación.',
        'btn-download-toolkit-2': 'Descargar Herramientas',
        'workplace-certification-title': 'Certificación de Lugar de Trabajo',
        'workplace-certification-desc': 'Obtenga la certificación oficial de su empresa como un Lugar de Trabajo 4A y reciba materiales para mostrar su compromiso con los derechos constitucionales.',
        'btn-get-certified': 'Obtener Certificación',
        'training-videos-title': 'Videos de Entrenamiento',
        'training-videos-desc': 'Videos educativos que explican los derechos en el lugar de trabajo y las respuestas apropiadas a la aplicación de la ley de inmigración.',
        'btn-watch-videos': 'Ver Videos',
        'btn-coming-soon': 'Próximamente'
    };

    // Apply translations to elements with data-translate attributes
    Object.keys(translations).forEach(key => {
        const element = document.querySelector(`[data-translate="${key}"]`);
        if (element) {
            element.innerHTML = translations[key];
        } else {
            console.log(`Spanish translation key not found: ${key}`);
        }
    });
}

function translateToEnglish() {
    // General site elements
    const orgName = document.querySelector('.org-name');
    const heroTitle = document.querySelector('.hero h1');
    const heroSubtitle = document.querySelector('.hero p');

    if (orgName) orgName.textContent = 'Love Thy Neighbor WNC';
    if (heroTitle && !heroTitle.hasAttribute('data-translate')) {
        heroTitle.textContent = 'Love Thy Neighbor Western North Carolina';
    }
    if (heroSubtitle && !heroSubtitle.hasAttribute('data-translate')) {
        heroSubtitle.textContent = 'Standing with our immigrant neighbors through faith, advocacy, and community support';
    }

    // Fourth Amendment page specific English translations
    const englishTranslations = {
        'hero-title': 'Know Your Fourth Amendment Rights',
        'hero-subtitle': 'Understanding your constitutional protections in different settings and situations',
        'btn-workplace': 'Workplace Rights',
        'btn-emergency': 'Emergency Info',
        'btn-alerts': 'Get Alerts',

        'overview-title': 'Your Constitutional Rights',
        'overview-p1': 'The Fourth Amendment to the U.S. Constitution protects all people in the United States from unreasonable searches and seizures by government officials. These rights apply regardless of immigration status.',
        'overview-p2': 'Understanding your rights and how they apply in different settings can help protect you, your family, and your community. Knowledge is power, and knowing your rights is the first step in protecting them.',
        'overview-p3': '<strong>Esta información también está disponible en español.</strong> All rights information and workplace procedures are available in both English and Spanish to ensure everyone in our community can access this vital information.',

        'workplace-title': 'Workplace Rights',

        // Workplace Rights Cards
        'rights-card1-title': 'Your Fundamental Rights',
        'rights-card1-desc': 'You do not have to comply with unlawful requests from federal agents. These rights apply to all workers regardless of immigration status.',
        'rights-card1-li1': '<strong>Remain Silent:</strong> No one is required to answer questions about immigration status, country of origin, or who is present',
        'rights-card1-li2': '<strong>Withhold Documents:</strong> Do not show ID, timecards, schedules, or other records unless legally required',
        'rights-card1-li3': '<strong>Record Interactions:</strong> You can record and save footage of any encounter',
        'rights-card1-li4': '<strong>Alert Others:</strong> Staff have the right to notify others that federal agents are present',

        'rights-card2-title': 'Warrant Requirements',
        'rights-card2-desc': 'Agents cannot enter private areas without a valid judicial warrant. Understanding warrant types is crucial for protecting your workplace.',
        'rights-card2-li1': '<strong>Judicial Warrant:</strong> Signed by a judge, says "US District Court" at the top',
        'rights-card2-li2': '<strong>Administrative Warrant (I-200):</strong> Signed by DHS/ICE agent - you do NOT have to comply',
        'rights-card2-li3': '<strong>Restrict Access:</strong> Staff may say they\'re not authorized to allow entry to private areas',
        'rights-card2-li4': '<strong>Request Legal Counsel:</strong> Ask that any warrant be reviewed by legal counsel before granting access',

        'rights-card3-title': 'Legal Jeopardy Warning',
        'rights-card3-desc': 'While you have many rights, certain actions could put you in legal jeopardy. Know what to avoid.',
        'rights-card3-li1': '<strong>Refusing entry</strong> after a valid judicial warrant is presented',
        'rights-card3-li2': '<strong>Giving false information</strong> to federal agents',
        'rights-card3-li3': '<strong>Helping someone flee</strong> from law enforcement',
        'rights-card3-li4': '<strong>Remember:</strong> You can refuse searches and remain silent without legal consequences',

        // Procedures Section
        'procedures-title': 'If Federal Agents Enter Your Workplace',

        'proc-card1-title': 'Immediate Actions',
        'proc-card1-li1': '<strong>Film the interaction</strong> - Document everything safely',
        'proc-card1-li2': '<strong>Ask for identification:</strong> "What agency are you with? Can you show me an ID?"',
        'proc-card1-li3': '<strong>Request warrant:</strong> "Please show me the warrant"',
        'proc-card1-li4': '<strong>Lock interior doors</strong> separating public and private areas',

        'proc-card2-title': 'Warrant Review',
        'proc-card2-li1': '<strong>Judicial warrant:</strong> Must be signed by a judge with "US District Court" at top',
        'proc-card2-li2': '<strong>Administrative warrant (I-200):</strong> You do NOT have to comply',
        'proc-card2-li3': '<strong>If valid judicial warrant:</strong> Consult with legal counsel before granting access',
        'proc-card2-li4': '<strong>Monitor compliance:</strong> Ensure agents only search areas listed in warrant',

        'proc-card3-title': 'Protect Everyone',
        'proc-card3-li1': '<strong>Alert all staff:</strong> "All staff, please stay calm and follow our federal agent protocols"',
        'proc-card3-li2': '<strong>Inform loudly:</strong> "You do not have to answer questions or show identification"',
        'proc-card3-li3': '<strong>Do NOT help agents</strong> sort people by immigration status or country of origin',
        'proc-card3-li4': '<strong>State clearly:</strong> "You do not have my permission to enter" private areas',

        // Workplace Readiness Checklist
        'readiness-title': 'Workplace Readiness Checklist',
        'readiness-desc': 'Prepare your workplace to protect everyone\'s rights. Use this checklist to ensure your organization is ready.',

        'ready-card1-title': 'Physical Preparations',
        'ready-card1-li1': '✓ Post signs marking PRIVATE areas',
        'ready-card1-li2': '✓ Install locks on interior doors separating public and private areas',
        'ready-card1-li3': '✓ Ensure staff can quickly secure private areas',
        'ready-card1-li4': '✓ Post clear protocols that all staff must follow',

        'ready-card2-title': 'Staff Training',
        'ready-card2-li1': '✓ Designate qualified person to manage agent interactions',
        'ready-card2-li2': '✓ Train staff on federal agent protocols',
        'ready-card2-li3': '✓ Establish rapid notification system for staff',
        'ready-card2-li4': '✓ Practice emergency procedures regularly',

        'ready-card3-title': 'Legal Preparedness',
        'ready-card3-li1': '✓ Have legal counsel contact information readily available',
        'ready-card3-li2': '✓ Know the difference between warrant types',
        'ready-card3-li3': '✓ Understand your rights and limitations',
        'ready-card3-li4': '✓ Keep emergency contact numbers posted',

        // If An Agent Shows Up page English translations
        'agent-hero-title': 'If An Agent Shows Up',
        'agent-hero-subtitle': 'Emergency response guide for federal agent encounters',
        'btn-immediate': 'Immediate Actions',
        'btn-contacts': 'Emergency Contacts',

        'agent-overview-title': 'Stay Safe and Know Your Rights',
        'agent-overview-p1': 'If federal agents appear at your workplace, home, or in your community, it\'s important to stay calm and know your rights. This guide provides step-by-step instructions for protecting yourself and others.',
        'agent-overview-p2': 'Remember: You have constitutional rights regardless of your immigration status. These rights protect you from unreasonable searches and seizures by government officials.',
        'agent-overview-p3': '<strong>Esta información también está disponible en español.</strong> All emergency response information is available in both English and Spanish to ensure everyone in our community can access this vital information.',

        'immediate-title': 'Immediate Actions',
        'action-card1-title': 'Document Everything',
        'action-card1-desc': 'If it\'s safe to do so, record the interaction and document what you see.',
        'action-card1-li1': '<strong>Film the interaction</strong> - Document everything safely from a distance',
        'action-card1-li2': '<strong>Note details:</strong> Time, location, number of agents, badge numbers',
        'action-card1-li3': '<strong>Save footage:</strong> Upload to cloud storage immediately',
        'action-card1-li4': '<strong>Share with legal aid:</strong> Contact support organizations',

        'action-card2-title': 'Verify Authority',
        'action-card2-desc': 'Always ask for identification and verify any warrants presented.',
        'action-card2-li1': '<strong>Ask for ID:</strong> "What agency are you with? Can you show me an ID?"',
        'action-card2-li2': '<strong>Request warrant:</strong> "Please show me the warrant"',
        'action-card2-li3': '<strong>Check validity:</strong> Judicial warrant must be signed by a judge',
        'action-card2-li4': '<strong>Administrative warrant (I-200):</strong> You do NOT have to comply',

        'action-card3-title': 'Alert and Protect',
        'action-card3-desc': 'Warn others and help protect everyone\'s rights.',
        'action-card3-li1': '<strong>Alert others loudly:</strong> "Federal agents are here"',
        'action-card3-li2': '<strong>Inform of rights:</strong> "You do not have to answer questions or show ID"',
        'action-card3-li3': '<strong>Lock private areas:</strong> Secure interior doors if possible',
        'action-card3-li4': '<strong>Call for help:</strong> Contact legal aid and family members',

        'rights-title': 'Your Rights During an Encounter',
        'rights-card1-title': 'Right to Remain Silent',
        'rights-card1-desc': 'You are not required to answer questions about immigration status, country of origin, or who is present.',
        'rights-card1-li1': 'Say: "I am exercising my right to remain silent"',
        'rights-card1-li2': 'Do not provide information about others',
        'rights-card1-li3': 'You can ask for an interpreter',
        'rights-card1-li4': 'Silence cannot be used against you',

        'rights-card2-title': 'Right to Refuse Searches',
        'rights-card2-desc': 'You do not have to consent to searches of your person, belongings, or private areas without a valid judicial warrant.',
        'rights-card2-li1': 'Say: "I do not consent to a search"',
        'rights-card2-li2': 'Do not show ID unless legally required',
        'rights-card2-li3': 'Do not open doors or containers',
        'rights-card2-li4': 'Ask to see a valid judicial warrant',

        'rights-card3-title': 'Right to Legal Counsel',
        'rights-card3-desc': 'You have the right to speak with an attorney and to have legal counsel review any warrants.',
        'rights-card3-li1': 'Say: "I want to speak with my attorney"',
        'rights-card3-li2': 'Ask for warrant review by legal counsel',
        'rights-card3-li3': 'Contact legal aid organizations',
        'rights-card3-li4': 'Do not sign anything without legal review',

        'contacts-title': 'Emergency Contacts',
        'contacts-desc': 'Keep these numbers readily available and share them with family members and coworkers.',
        'contact-card1-title': 'Crisis Hotline',
        'contact-card1-desc': '24/7 emergency support for immigration enforcement situations',
        'contact-card2-title': 'Legal Aid',
        'contact-card2-desc': 'Free legal assistance for immigration and constitutional rights issues',
        'contact-card3-title': 'Community Support',
        'contact-card3-desc': 'Local organizations providing immediate assistance and resources',

        // Workplace Rights page English translations
        'workplace-hero-title': 'Fourth Amendment Workplace Rights',
        'workplace-hero-subtitle': 'Protecting constitutional rights in the workplace',
        'btn-rights': 'Your Rights',
        'btn-readiness': 'Workplace Readiness',

        'workplace-overview-title': 'Your Constitutional Protections at Work',
        'workplace-overview-p1': 'The Fourth Amendment to the U.S. Constitution protects all people in the United States from unreasonable searches and seizures by government officials. These rights apply in workplace settings regardless of immigration status.',
        'workplace-overview-p2': 'Understanding your rights and how they apply in your workplace can help protect you, your coworkers, and your community. Knowledge is power, and knowing your rights is the first step in protecting them.',
        'workplace-overview-p3': '<strong>Esta información también está disponible en español.</strong> All workplace rights information and procedures are available in both English and Spanish to ensure everyone in our community can access this vital information.',

        // Resources page translations
        'resources-hero-title': 'Resources & Toolkits',
        'resources-hero-subtitle': 'Educational materials and guides to support immigrant justice work',
        'immigrant-resources-title': 'Immigrant Resources',
        'know-rights-cards-title': 'Know Your Rights Cards',
        'know-rights-cards-desc': 'Printable cards with essential rights information in English and Spanish. Keep in wallet or purse.',
        'btn-download-pdf': 'Download PDF',
        'facts-matter-title': 'Facts Matter',
        'facts-matter-desc': 'Read the 7 Facts on Immigration Every American Should Know - Click Here',
        'btn-read-facts': 'Read Facts',
        'whose-faith-title': 'Whose Faith matters? The Fight for Religious Liberty Beyond the Christian Right',
        'whose-faith-quote': '"Data-driven research affirms the essential role that immigrants—regardless of legal status—play in strengthening the U.S. economy, enriching communities, and contributing to the nation\'s workforce. Immigrants pay billions in taxes, support vital programs like Social Security, and help drive innovation, care work, and small business development. Policies that target immigrants for removal often result in widespread harm—separating families, disrupting local economies, and undermining community stability. At the same time, a clear majority of Americans support inclusive immigration policies, including pathways to citizenship, recognizing that our shared future is more secure when rooted in dignity, opportunity, and fairness. Read on for seven key facts about immigration that every American should know."',
        'whose-faith-attribution': '— Clara Alvarez Caraveo, PhD student at USC Sociology and Jody Agius Vallejo, Professor of Sociology',
        'whose-faith-description': '_This report is intended as a_ **_practical tool_** _for immigration attorneys, criminal defense lawyers, civil rights advocates, faith-based organizations, and houses of worship._',
        'btn-read-facts-2': 'Read Facts',
        'collective-statement-title': 'Collective Statement by Faith Communities in Support of Immigrant Justice',
        'collective-statement-desc': 'Together in faith and rooted in love, we resolve to continue in the centuries-old practice of Christian communities walking alongside refugees and immigrants in their pursuit of safety and dignity. We pledge to restore and promote hospitality and welcome to those seeking refuge – regardless of where they are from, how they pray or what language they speak.',
        'btn-view-guide': 'View Guide',
        'educational-rights-title': 'Educational Rights',
        'educational-rights-p1': '<strong>As faith-based organizations and institutions rooted in Western North Carolina and representing a diversity of religious traditions and belief, we are united in our commitment to working towards a humane and just world for all, regardless of immigration status.</strong>',
        'educational-rights-p2': 'We believe that all people are Beloved Children of God, important and vital members of our community, and deserving of the full enjoyment of human rights and human dignity. We believe in loving one\'s neighbor, welcoming the stranger, providing safe harbor for those fleeing oppression, caring for the vulnerable among us, and standing against injustice.',
        'btn-read-statement': 'Read Full Statement',
        'faith-toolkit-title': 'Faith Community Toolkit',
        'faith-toolkit-desc': 'Resources to help faith communities become more welcoming and supportive of immigrant neighbors, including sanctuary policies, educational programs, and advocacy tools.',
        'sanctuary-policies-title': 'Sanctuary Policies',
        'sanctuary-policies-desc': 'Template policies and procedures for congregations considering sanctuary designation.',
        'btn-download-toolkit': 'Download Toolkit',
        'educational-curricula-title': 'Educational Curricula',
        'educational-curricula-desc': 'Bible studies, discussion guides, and educational materials on immigration and faith.',
        'btn-access-materials': 'Access Materials',
        'accompaniment-training-title': 'Accompaniment Training',
        'accompaniment-training-desc': 'Training materials for congregation members who want to accompany immigrants to court or ICE check-ins.',
        'btn-get-training': 'Get Training',
        '4a-workplaces-title': '4A Workplaces Campaign Resources',
        '4a-workplaces-desc': 'Comprehensive resources for businesses and workers to understand and protect Fourth Amendment rights in workplace settings. Join our growing network of constitutional sanctuary workplaces.',
        '4a-campaign-title': '4A Workplaces Campaign',
        '4a-campaign-desc': 'Learn about our flagship initiative to create constitutional sanctuary workplaces. Over 50 businesses have already joined our network.',
        'btn-join-campaign': 'Join the Campaign',
        'employer-toolkit-title': 'Employer Toolkit',
        'employer-toolkit-desc': 'Complete toolkit for employers including policies, procedures, training materials, and certification information.',
        'btn-download-toolkit-2': 'Download Toolkit',
        'workplace-certification-title': 'Workplace Certification',
        'workplace-certification-desc': 'Get your business officially certified as a 4A Workplace and receive materials to display your commitment to constitutional rights.',
        'btn-get-certified': 'Get Certified',
        'training-videos-title': 'Training Videos',
        'training-videos-desc': 'Educational videos explaining workplace rights and proper responses to immigration enforcement.',
        'btn-watch-videos': 'Watch Videos',
        'btn-coming-soon': 'Coming Soon'
    };

    // Apply English translations to elements with data-translate attributes
    Object.keys(englishTranslations).forEach(key => {
        const element = document.querySelector(`[data-translate="${key}"]`);
        if (element) {
            element.innerHTML = englishTranslations[key];
        } else {
            console.log(`English translation key not found: ${key}`);
        }
    });
}

// Utility function for future server communication
function submitToServer(data) {
    // TODO: Implement actual server submission
    // This would typically use fetch() or XMLHttpRequest
    /*
    fetch('/api/email-signup', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        console.log('Success:', result);
        alert('Thank you for signing up!');
        closeEmailModal();
    })
    .catch(error => {
        console.error('Error:', error);
        alert('There was an error signing up. Please try again.');
    });
    */
}
