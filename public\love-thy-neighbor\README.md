# Love Thy Neighbor WNC Website

Welcome! This is the website for Love Thy Neighbor WNC, an organization that supports immigrant communities in Western North Carolina. This guide will help you understand what's here and how to navigate the website files.

## 🌟 What This Website Does

This website helps people:
- Learn about Love Thy Neighbor WNC's mission and work
- Find ways to get involved and take action
- Access resources for immigrant communities
- Connect with partner organizations
- Get help through mutual aid programs

## 📁 What's In This Folder

Here's what each file and folder does, explained in simple terms:

### 🏠 **Main Website Pages** (the pages people visit)
- **`index.php`** - The homepage (what people see first)
- **`about.php`** - Information about the organization
- **`take-action.php`** - Ways people can help and get involved
- **`partners.php`** - Organizations we work with
- **`resources.php`** - Helpful information and materials
- **`mutual-aid.php`** - Community support programs
- **`contact.php`** - How to reach us

### 📂 **Page Sections** (organized by topic)
- **`about/`** folder contains:
  - `mission-values.php` - Our mission and what we believe
  - `our-story.php` - How the organization started and grew
  - `team.php` - The people who work here
- **`take-action/`** folder contains:
  - `yard-signs.php` - Information about our yard sign campaign

### 🔧 **Behind-the-Scenes Files** (make the website work)
- **`includes/`** folder - Contains pieces used on every page:
  - `header.php` - The top part of every page (logo, navigation)
  - `footer.php` - The bottom part of every page (contact info, links)
  - `config.php` - Settings and information used throughout the site
  - `head.php` - Technical stuff that makes pages load properly
  - `scripts.php` - Interactive features (like pop-up windows)
  - `page-template.php` - A starting point for creating new pages

- **`assets/`** folder - Design and interactive elements:
  - `css/styles.css` - Makes the website look good (colors, fonts, layout)
  - `js/main.js` - Makes interactive features work (buttons, menus)

### 📄 **Other Important Files**
- **`.htaccess`** - Technical settings for the web server
- **`README.md`** - This file you're reading now!

## 🎯 How to Use This Website

### For Visitors (People Using the Website)
1. Start at `index.php` (the homepage)
2. Use the navigation menu to explore different sections
3. Click on dropdown menus to find specific information
4. Use the contact form or information to get in touch

### For Content Editors (People Updating the Website)
1. **To change contact information**: Edit `includes/config.php`
2. **To update page content**: Edit the specific `.php` file for that page
3. **To change colors or fonts**: Edit `assets/css/styles.css`
4. **To add a new page**: Copy `includes/page-template.php` and customize it

## 🔍 Finding What You Need

**Want to change...?**
- **Contact info** → `includes/config.php`
- **Homepage content** → `index.php`
- **About page content** → `about.php` or files in `about/` folder
- **Website colors/design** → `assets/css/styles.css`
- **Navigation menu** → `includes/config.php` (navigation section)
- **Footer links** → `includes/footer.php`

## 💡 Tips for Non-Technical Users

- **Don't worry about breaking anything** - you can always undo changes
- **Make small changes first** - test one thing at a time
- **Keep backups** - save copies of files before making big changes
- **Ask for help** - if something doesn't work, reach out to someone technical

## 🚀 Getting Started

1. **To view the website**: Open `index.php` in a web browser
2. **To make changes**: Use a text editor to open and edit `.php` files
3. **To test changes**: Save the file and refresh your web browser
4. **To add content**: Look at existing pages as examples

## 📞 Need Help?

If you need assistance with the website:
- Look at existing pages for examples of how things work
- Make small changes and test them
- Keep notes about what you change
- Don't hesitate to ask someone technical for help

---

*This website was built to be easy to update and maintain. Each page is designed to work independently while sharing common elements like the header and footer.*
