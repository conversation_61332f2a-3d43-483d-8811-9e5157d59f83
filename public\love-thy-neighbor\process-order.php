<?php
/**
 * Process Yard Sign Orders - Love Thy Neighbor WNC
 * Handles form submissions for yard sign orders
 */

// Include configuration
require_once 'includes/config.php';

// Set content type for JSON response
header('Content-Type: application/json');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

// Validate required fields
$required_fields = ['firstName', 'lastName', 'email', 'phone', 'address', 'signType', 'quantity', 'fulfillmentMethod'];
$missing_fields = [];

foreach ($required_fields as $field) {
    if (empty($input[$field])) {
        $missing_fields[] = $field;
    }
}

if (!empty($missing_fields)) {
    http_response_code(400);
    echo json_encode(['error' => 'Missing required fields: ' . implode(', ', $missing_fields)]);
    exit;
}

// Calculate pricing
$prices = [
    'standard' => 15,
    'window' => 5,
    'banner' => 45
];

$sign_type = $input['signType'];
$quantity = intval($input['quantity']);
$unit_price = $prices[$sign_type] ?? 0;
$total_cost = $unit_price * $quantity;

// Prepare order data
$order_data = [
    'firstName' => htmlspecialchars($input['firstName']),
    'lastName' => htmlspecialchars($input['lastName']),
    'email' => filter_var($input['email'], FILTER_VALIDATE_EMAIL),
    'phone' => htmlspecialchars($input['phone']),
    'address' => htmlspecialchars($input['address']),
    'signType' => $sign_type,
    'quantity' => $quantity,
    'fulfillmentMethod' => $input['fulfillmentMethod'],
    'notes' => htmlspecialchars($input['notes'] ?? ''),
    'unitPrice' => $unit_price,
    'totalCost' => $total_cost,
    'orderDate' => date('Y-m-d H:i:s'),
    'orderNumber' => 'LTN-' . date('Ymd') . '-' . substr(md5(uniqid()), 0, 6)
];

// Validate email
if (!$order_data['email']) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid email address']);
    exit;
}

// TODO: Configure mail settings
// For now, we'll log the order and send a simple response
// In production, you would:
// 1. Save order to database
// 2. Send confirmation email to customer
// 3. Send notification email to administrators
// 4. Generate invoice

// Log the order (for development)
$log_entry = date('Y-m-d H:i:s') . " - New Order: " . json_encode($order_data) . "\n";
file_put_contents('orders.log', $log_entry, FILE_APPEND | LOCK_EX);

// Send success response
echo json_encode([
    'success' => true,
    'message' => 'Order received successfully!',
    'orderNumber' => $order_data['orderNumber'],
    'totalCost' => $total_cost,
    'fulfillmentMethod' => $order_data['fulfillmentMethod']
]);

// TODO: Implement email functionality
// Example mail configuration would go here:
/*
// Configure mail settings
$mail_config = [
    'smtp_host' => 'your-smtp-server.com',
    'smtp_port' => 587,
    'smtp_username' => '<EMAIL>',
    'smtp_password' => 'your-password',
    'from_email' => CONTACT_EMAIL,
    'admin_email' => CONTACT_EMAIL
];

// Send customer confirmation email
$customer_subject = "Order Confirmation - " . $order_data['orderNumber'];
$customer_message = "Thank you for your order! We'll contact you soon about " . $order_data['fulfillmentMethod'] . ".";

// Send admin notification email
$admin_subject = "New Yard Sign Order - " . $order_data['orderNumber'];
$admin_message = "New order received:\n\n" . print_r($order_data, true);

// Use PHPMailer or similar library to send emails
*/
?>
