<?php
/**
 * Header include file
 * Contains the site header with navigation
 */

// Include configuration if not already included
if (!defined('SITE_NAME')) {
    require_once 'config.php';
}
?>

<!-- Header -->
<header>
    <div class="container header-container">
        <div class="logo">
            <img src="<?php echo get_asset_url('assets/LTN.png'); ?>" alt="Love Thy Neighbor WNC Logo" class="logo-image">
            <div class="org-name"><?php echo SITE_NAME; ?></div>
        </div>
        
        <!-- Mobile Menu Toggle Button -->
        <button class="mobile-menu-toggle" id="mobileMenuToggle" aria-label="Toggle navigation menu">
            <span class="hamburger-line"></span>
            <span class="hamburger-line"></span>
            <span class="hamburger-line"></span>
        </button>

        <nav class="main-navigation" id="mainNavigation">
            <ul class="main-nav">
                <?php foreach ($nav_items as $item): ?>
                    <li class="nav-item<?php echo isset($item['submenu']) ? ' has-submenu' : ''; ?>">
                        <a href="<?php echo htmlspecialchars(get_nav_url($item['href'])); ?>"><?php echo htmlspecialchars($item['text']); ?></a>
                        <?php if (isset($item['submenu'])): ?>
                            <ul class="submenu">
                                <?php foreach ($item['submenu'] as $subitem): ?>
                                    <li><a href="<?php echo htmlspecialchars(get_nav_url($subitem['href'])); ?>"><?php echo htmlspecialchars($subitem['text']); ?></a></li>
                                <?php endforeach; ?>
                            </ul>
                        <?php endif; ?>
                    </li>
                <?php endforeach; ?>
            </ul>
            <div class="language-switcher">
                <select class="language-dropdown" id="languageSelect">
                    <?php foreach ($languages as $code => $name): ?>
                        <option value="<?php echo htmlspecialchars($code); ?>"><?php echo htmlspecialchars($name); ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
        </nav>
    </div>
</header>
