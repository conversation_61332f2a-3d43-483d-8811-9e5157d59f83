<?php
/**
 * HTML Head section include
 * Contains meta tags, CSS, and other head elements
 */

// Include configuration
require_once 'config.php';

// Set default values if not provided
$page_title = isset($page_title) ? $page_title : '';
$page_description = isset($page_description) ? $page_description : $default_meta['description'];
$page_keywords = isset($page_keywords) ? $page_keywords : $default_meta['keywords'];
$page_author = isset($page_author) ? $page_author : $default_meta['author'];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo get_page_title($page_title); ?></title>
    
    <!-- Meta Tags -->
    <meta name="description" content="<?php echo htmlspecialchars($page_description); ?>">
    <meta name="keywords" content="<?php echo htmlspecialchars($page_keywords); ?>">
    <meta name="author" content="<?php echo htmlspecialchars($page_author); ?>">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo get_page_title($page_title); ?>">
    <meta property="og:description" content="<?php echo htmlspecialchars($page_description); ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo SITE_URL; ?>">
    <meta property="og:site_name" content="<?php echo SITE_NAME; ?>">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo get_page_title($page_title); ?>">
    <meta name="twitter:description" content="<?php echo htmlspecialchars($page_description); ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo get_asset_url('peaceswan48x48.ico'); ?>">
    
    <!-- External CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@splidejs/splide@4.1.4/dist/css/splide.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo get_asset_url('assets/css/styles.css'); ?>?v=<?php echo CSS_VERSION; ?>">

    <!-- Additional CSS files can be included here -->
    <?php if (isset($additional_css) && is_array($additional_css)): ?>
        <?php foreach ($additional_css as $css_file): ?>
            <link rel="stylesheet" href="<?php echo get_asset_url($css_file); ?>?v=<?php echo CSS_VERSION; ?>">
        <?php endforeach; ?>
    <?php endif; ?>
</head>
<body<?php echo isset($body_class) ? ' class="' . htmlspecialchars($body_class) . '"' : ''; ?>>
