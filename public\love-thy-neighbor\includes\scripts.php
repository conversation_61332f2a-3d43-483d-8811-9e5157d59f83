<?php
/**
 * Scripts include file
 * Contains JavaScript files and inline scripts
 */

// Include configuration if not already included
if (!defined('SITE_NAME')) {
    require_once 'config.php';
}
?>

<!-- Email <PERSON> -->
<button class="email-alert-btn" onclick="openEmailModal()">
    <i class="fas fa-envelope"></i> Email Alerts
</button>

<!-- Email Signup Modal -->
<div id="emailModal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="closeEmailModal()">&times;</span>
        <h2>Sign Up for Email Alerts</h2>
        <p>Stay informed about immigration rights, community events, and urgent alerts in Western North Carolina.</p>
        <form id="emailSignupForm" onsubmit="submitEmailForm(event)">
            <div class="form-group">
                <label for="firstName">First Name:</label>
                <input type="text" id="firstName" name="firstName" required>
            </div>
            <div class="form-group">
                <label for="lastName">Last Name:</label>
                <input type="text" id="lastName" name="lastName" required>
            </div>
            <div class="form-group">
                <label for="email">Email Address:</label>
                <input type="email" id="email" name="email" required>
            </div>
            <div class="form-group">
                <label for="zipCode">Zip Code:</label>
                <input type="text" id="zipCode" name="zipCode" placeholder="Optional">
            </div>
            <button type="submit" class="btn btn-primary" style="width: 100%;">Sign Up for Alerts</button>
        </form>
    </div>
</div>

<!-- Yard Sign Order Modal -->
<div id="yardSignModal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="closeYardSignModal()">&times;</span>
        <h2>Order Yard Signs</h2>
        <p>Fill out the form below to order your Love Thy Neighbor yard signs. We'll send you an invoice and arrange pickup or delivery.</p>
        <form id="yardSignOrderForm" onsubmit="submitYardSignOrder(event)">
            <!-- Name Row -->
            <div class="form-row">
                <div class="form-group form-group-half">
                    <label for="orderFirstName">First Name:</label>
                    <input type="text" id="orderFirstName" name="firstName" required>
                </div>
                <div class="form-group form-group-half">
                    <label for="orderLastName">Last Name:</label>
                    <input type="text" id="orderLastName" name="lastName" required>
                </div>
            </div>

            <!-- Contact Row -->
            <div class="form-row">
                <div class="form-group form-group-half">
                    <label for="orderEmail">Email Address:</label>
                    <input type="email" id="orderEmail" name="email" required>
                </div>
                <div class="form-group form-group-half">
                    <label for="orderPhone">Phone Number:</label>
                    <input type="tel" id="orderPhone" name="phone" required>
                </div>
            </div>

            <!-- Address -->
            <div class="form-group">
                <label for="orderAddress">Address:</label>
                <textarea id="orderAddress" name="address" rows="2" placeholder="Street address, city, state, zip code" required></textarea>
            </div>

            <!-- Order Details Row -->
            <div class="form-row">
                <div class="form-group form-group-two-thirds">
                    <label for="signType">Sign Type:</label>
                    <select id="signType" name="signType" required>
                        <option value="">Select a sign type</option>
                        <option value="standard">Standard Yard Sign - $15</option>
                        <option value="window">Window Cling - $5</option>
                        <option value="banner">Large Banner - $45</option>
                    </select>
                </div>
                <div class="form-group form-group-third">
                    <label for="quantity">Quantity:</label>
                    <input type="number" id="quantity" name="quantity" min="1" value="1" required>
                </div>
            </div>

            <!-- Fulfillment Method -->
            <div class="form-group">
                <label for="fulfillmentMethod">Fulfillment Method:</label>
                <select id="fulfillmentMethod" name="fulfillmentMethod" required>
                    <option value="">Select pickup or delivery</option>
                    <option value="pickup">Pickup (we'll contact you with location)</option>
                    <option value="delivery">Delivery (additional fees may apply)</option>
                </select>
            </div>

            <!-- Notes -->
            <div class="form-group">
                <label for="orderNotes">Special Notes (Optional):</label>
                <textarea id="orderNotes" name="notes" rows="2" placeholder="Any special instructions or requests"></textarea>
            </div>

            <button type="submit" class="btn btn-primary order-submit-btn">Submit Order</button>
        </form>
    </div>
</div>

<!-- External JavaScript Libraries -->
<script src="https://cdn.jsdelivr.net/npm/@splidejs/splide@4.1.4/dist/js/splide.min.js"></script>

<!-- Core JavaScript -->
<script src="<?php echo get_asset_url('assets/js/main.js'); ?>?v=<?php echo JS_VERSION; ?>"></script>

<!-- Additional JavaScript files can be included here -->
<?php if (isset($additional_js) && is_array($additional_js)): ?>
    <?php foreach ($additional_js as $js_file): ?>
        <script src="<?php echo get_asset_url($js_file); ?>?v=<?php echo JS_VERSION; ?>"></script>
    <?php endforeach; ?>
<?php endif; ?>

<!-- Inline JavaScript for page-specific functionality -->
<?php if (isset($inline_js) && !empty($inline_js)): ?>
    <script>
        <?php echo $inline_js; ?>
    </script>
<?php endif; ?>

</body>
</html>
