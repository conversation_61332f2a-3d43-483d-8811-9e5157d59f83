# Love Thy Neighbor WNC - Apache Configuration

# Enable rewrite engine
RewriteEngine On

# Set default index file to index.php
DirectoryIndex index.php index.html

# Security: Prevent access to includes directory
<Files "includes/*">
    Order Allow,Deny
    Deny from all
</Files>

# Security: Prevent access to configuration files
<Files "*.config">
    Order Allow,<PERSON>y
    Deny from all
</Files>

# Security: Prevent access to backup files
<Files "*~">
    Order Allow,Deny
    Deny from all
</Files>

# Security: Prevent access to version control files
<Files ".git*">
    Order Allow,Deny
    Deny from all
</Files>

# Enable compression for better performance
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Set cache headers for static assets
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/ico "access plus 1 month"
    ExpiresByType image/icon "access plus 1 month"
    ExpiresByType text/x-icon "access plus 1 month"
    ExpiresByType application/x-icon "access plus 1 month"
</IfModule>

# Optional: Clean URLs (remove .php extension)
# Uncomment the following lines if you want clean URLs
# RewriteCond %{REQUEST_FILENAME} !-d
# RewriteCond %{REQUEST_FILENAME} !-f
# RewriteRule ^([^\.]+)$ $1.php [NC,L]

# Optional: Redirect www to non-www (uncomment if needed)
# RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
# RewriteRule ^(.*)$ http://%1/$1 [R=301,L]

# Optional: Force HTTPS (uncomment if you have SSL)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
