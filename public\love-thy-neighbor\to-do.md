Logo files in high-resolution formats (AI, EPS, SVG, PNG)

Brand guidelines (colors, fonts, logo usage rules)

Professional photos of:

Team members (headshots)

Community events

Faith community gatherings

Yard signs displayed

Partner logos in high-resolution formats

Written content for:

Mission statement and values

Organization history

Team bios

Program descriptions

Highly Recommended Assets:
Testimonials from community members (with permission to share)

Success stories (anonymized as needed)

Video content (short clips of events, interviews, etc.)

Social media handles and existing content

Newsletter content if they have an existing one

Helpful Documents & Information:
"Know Your Rights" materials they want to feature

Yard sign campaign details (pricing, how to order, distribution process)

Partner organization descriptions and contact information

Specific calls to action they want visitors to take

Donation information (what funds support, how donations are used)

Development To-Do List
Phase 1: Planning & Content
Finalize site structure and page hierarchy

Collect all required assets from the organization

Create content outline for each page

Define clear calls-to-action for each section

Plan multilingual strategy (English/Spanish)

Phase 2: Squarespace Setup
Choose and customize a Squarespace template

Set up navigation structure

Create all page shells

Configure domain settings

Set up basic SEO elements

Phase 3: Content Population
Add and format all text content

Upload and place all images

Create resource galleries/downloads

Set up forms (contact, newsletter signup)

Implement donation functionality

Phase 4: Customization & Polish
Apply custom CSS for branding

Test on different devices and browsers

Set up analytics (Google Analytics, etc.)

Implement social media integration

Test all links and forms

Phase 5: Launch & Maintenance
Final review with the team

Train content managers on updating the site

Create documentation for ongoing maintenance

Plan launch announcement

Set up regular content review schedule